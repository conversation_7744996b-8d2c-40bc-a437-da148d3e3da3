import { useState, useMemo, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Users, Calendar, TrendingUp, Percent, User, ShieldCheck, Trophy, History, Sparkles, ArrowRight,
  Plus, UserPlus, CalendarPlus, Search, LayoutDashboard, Medal, Handshake, Shuffle, Crown,
  UsersRound, ChevronDown, ChevronUp, Award, Flame
} from "lucide-react";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/context/AuthContext";
import { useGroup } from "@/lib/context/GroupContext";

// Enhanced components
import { EnhancedCard } from "@/components/ui/enhanced-card";
import { ResponsiveTable } from "@/components/ui/responsive-table";
import { DashboardSkeleton, PlayerCardSkeleton } from "@/components/ui/skeletons";
import { AnimatedButton } from "@/components/ui/animated-button";
import { useEnhancedToast } from "@/hooks/use-enhanced-toast";
import { EnhancedInput } from "@/components/ui/enhanced-input";
import { EmptyState } from "@/components/ui/empty-state";
import { useUserPreferences } from "@/context/UserPreferencesContext";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { format, parseISO } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";

// Dashboard-specific components
import DashboardLeaderboard from "@/components/dashboard/DashboardLeaderboard";
import DashboardChemistry from "@/components/dashboard/DashboardChemistry";
import DashboardTeamGenerator from "@/components/dashboard/DashboardTeamGenerator";

// localStorage Keys
const PLAYERS_STORAGE_KEY = 'soccerPlayersData';
const MATCHES_STORAGE_KEY = 'soccerMatchesData';

// --- Data Loading ---

interface Player {
  id: number;
  name: string;
  skills: number;
  effort: number;
  stamina: number;
  group_id?: string;
}

interface Goalscorer { team: 'A' | 'B'; playerId: number; }
interface Match { id: number; date: Date; teamA: number[]; teamB: number[]; scoreA: number | null; scoreB: number | null; winner?: 'A' | 'B' | 'Draw'; goalscorers?: Goalscorer[]; youtubeLink?: string; group_id?: string; }
interface ChemistryPair { player1: number; player2: number; played: number; wins: number; winRate: number; }
interface BestChemistry {
  players: number[];
  played: number;
  wins: number;
  winRate: number;
}

interface PlayerChemistryStats {
  duos: BestChemistry[];
  trios: BestChemistry[];
  quads: BestChemistry[];
}

interface Group {
  id: string;
  name: string;
  created_by: string;
  created_at: string;
}

// Function to load data from localStorage
function loadData<T>(key: string, fallback: T[]): T[] {
    try {
      const storedData = typeof window !== 'undefined' ? localStorage.getItem(key) : null;
      if (storedData) {
          const parsedData = JSON.parse(storedData) as any[];
          // Special handling for dates in matches
          if (key === MATCHES_STORAGE_KEY) {
              // Make date parsing more robust
              return parsedData.map(item => ({
                  ...item,
                  date: item.date ? parseISO(item.date) : new Date(0) // Use epoch if date is missing/invalid
              })) as T[];
          }
          return parsedData as T[];
      }
    } catch (error) {
      console.error(`Error reading ${key} from localStorage:`, error);
    }
    return fallback;
}

// Load players and matches data
const playersData = loadData<Player>(PLAYERS_STORAGE_KEY, []);
const matchesData = loadData<Match>(MATCHES_STORAGE_KEY, []);

// Placeholder for chemistry data
const chemistryData: ChemistryPair[] = [
  // This should likely be calculated from matchesData or loaded too
];

// --- Helper Functions ---

const calculateAverageRating = (player: Omit<Player, 'id' | 'name'>): number => {
  if (!player || typeof player.skills !== 'number' || typeof player.effort !== 'number' || typeof player.stamina !== 'number') return 0;
  return Math.round((player.skills + player.effort + player.stamina) / 3);
};

// Define a type for a completed World Cup Run
interface CompletedWorldCupRun {
  date: Date; // Date when the World Cup was won (date of the last knockout match)
}

// Define a type for the World Cup Run result
interface WorldCupRunResult {
  isWorldChampion: boolean;
  status: 'champion' | 'qualified' | 'in-progress' | 'eliminated' | 'not-started';
  groupStagePoints: number;
  groupStageMatchesPlayed: number;
  pointsNeededToQualify: number;
  knockoutMatchesWon: number;
  knockoutMatchesNeeded: number;
  currentRun: number; // How many World Cups won
  completedRuns: CompletedWorldCupRun[]; // Array of completed World Cup Runs with dates
}

// Calculate World Cup Run stat based on tournament simulation
const calculateWorldCupRun = (matches: Match[], playerId: number): WorldCupRunResult => {
  // Default result with no matches
  const defaultResult: WorldCupRunResult = {
    isWorldChampion: false,
    status: 'not-started',
    groupStagePoints: 0,
    groupStageMatchesPlayed: 0,
    pointsNeededToQualify: 4,
    knockoutMatchesWon: 0,
    knockoutMatchesNeeded: 4,
    currentRun: 0,
    completedRuns: []
  };

  // Get matches where this player participated, sorted by date (newest first)
  const playerMatches = matches
    .filter(match => (match.teamA?.includes(playerId) || match.teamB?.includes(playerId)))
    .sort((a, b) => {
      const dateA = a.date instanceof Date ? a.date : new Date(a.date);
      const dateB = b.date instanceof Date ? b.date : new Date(b.date);
      return dateB.getTime() - dateA.getTime(); // Sort newest first
    });

  // If no matches, return default result
  if (playerMatches.length === 0) return defaultResult;

  // Helper function to check if player won a match
  const didPlayerWin = (match: Match): boolean => {
    const isInTeamA = match.teamA?.includes(playerId);
    return (isInTeamA && match.winner === 'A') || (!isInTeamA && match.winner === 'B');
  };

  // Helper function to check if player lost a match
  const didPlayerLose = (match: Match): boolean => {
    const isInTeamA = match.teamA?.includes(playerId);
    return (isInTeamA && match.winner === 'B') || (!isInTeamA && match.winner === 'A');
  };

  // Helper function to check if match was a draw
  const isMatchDraw = (match: Match): boolean => {
    return match.winner === 'Draw';
  };

  // Helper function to check if a group stage is valid (at least 2 wins, or 1 win and 1 draw)
  const isValidGroupStage = (matches: Match[]): boolean => {
    if (matches.length !== 3) return false;

    let wins = 0;
    let draws = 0;

    matches.forEach(match => {
      if (didPlayerWin(match)) wins++;
      else if (isMatchDraw(match)) draws++;
    });

    // Valid if: 2+ wins OR 1 win + 1+ draws
    return wins >= 2 || (wins === 1 && draws >= 1);
  };

  // Helper function to calculate points for a group of matches
  const calculateGroupPoints = (matches: Match[]): number => {
    return matches.reduce((points, match) => {
      if (didPlayerWin(match)) return points + 3;
      if (isMatchDraw(match)) return points + 1;
      return points;
    }, 0);
  };

  // Track current World Cup run count and completed runs
  let worldCupRunCount = 0;
  let currentStatus: WorldCupRunResult['status'] = 'not-started';
  let currentGroupStagePoints = 0;
  let currentGroupStageMatchesPlayed = 0;
  let currentKnockoutMatchesWon = 0;
  let completedRuns: CompletedWorldCupRun[] = [];

  // Create a chronologically ordered copy of the matches (oldest first)
  // This ensures we evaluate the knockout stage chronologically after the group stage
  const chronologicalMatches = [...playerMatches].reverse();

  // Scan for completed World Cup runs and current status
  let i = 0;
  while (i <= chronologicalMatches.length - 3) {
    // Check if we have a valid group stage (3 consecutive matches)
    const potentialGroupStage = chronologicalMatches.slice(i, i + 3);

    if (isValidGroupStage(potentialGroupStage)) {
      // Found a valid group stage, now check for knockout matches that follow
      // Make sure there are enough matches after the group stage
      if (i + 3 + 4 <= chronologicalMatches.length) {
        // Get the 4 matches that follow the group stage
        const knockoutMatches = chronologicalMatches.slice(i + 3, i + 3 + 4);

        // Check if all knockout matches were wins
        const allKnockoutWins = knockoutMatches.every(match => didPlayerWin(match));

        if (allKnockoutWins) {
          // Found a valid World Cup run!
          worldCupRunCount++;

          // Get the date of the last knockout match (when the World Cup was won)
          const lastKnockoutMatch = knockoutMatches[knockoutMatches.length - 1];
          const winDate = lastKnockoutMatch.date instanceof Date
            ? lastKnockoutMatch.date
            : new Date(lastKnockoutMatch.date);

          // Add to completed runs
          completedRuns.push({ date: winDate });

          // Skip this entire run
          i += 7;
          continue;
        }
      }
    }

    // Move to the next potential starting point
    i++;
  }

  // Now determine the current status based on the most recent matches
  // Check if the last 3 matches form a valid group stage
  if (playerMatches.length >= 3) {
    const lastThreeMatches = playerMatches.slice(0, 3);
    const isValidGroup = isValidGroupStage(lastThreeMatches);
    const groupPoints = calculateGroupPoints(lastThreeMatches);

    if (isValidGroup) {
      // We have a valid group stage in the last 3 matches
      // For the current status, we don't need to look for knockout matches
      // since these are the most recent matches - the knockout stage would be in the future
      currentStatus = 'qualified';
      currentGroupStagePoints = groupPoints;
      currentGroupStageMatchesPlayed = 3;
      currentKnockoutMatchesWon = 0; // No knockout matches for the current run yet
    } else {
      // The last 3 matches don't form a valid group stage
      // Check if they're in progress for a group stage
      currentStatus = 'in-progress';
      currentGroupStagePoints = groupPoints;
      currentGroupStageMatchesPlayed = 3;
    }
  } else if (playerMatches.length > 0) {
    // Less than 3 matches, but some matches exist - in progress
    const points = calculateGroupPoints(playerMatches);
    currentStatus = 'in-progress';
    currentGroupStagePoints = points;
    currentGroupStageMatchesPlayed = playerMatches.length;
  }

  // Check if player is eliminated (lost last two matches)
  if (playerMatches.length >= 2) {
    const isCurrentlyEliminated = didPlayerLose(playerMatches[0]) && didPlayerLose(playerMatches[1]);
    if (isCurrentlyEliminated && currentStatus === 'in-progress') {
      currentStatus = 'eliminated';
    }
  }

  // Calculate points needed to qualify - need at least 4 points to qualify
  // 4 points can be achieved with 1 win and 1 draw (3 + 1) or 2 wins (3 + 3)
  const pointsNeededToQualify = Math.max(0, 4 - currentGroupStagePoints);

  return {
    isWorldChampion: currentStatus === 'champion',
    status: currentStatus,
    groupStagePoints: currentGroupStagePoints,
    groupStageMatchesPlayed: currentGroupStageMatchesPlayed,
    pointsNeededToQualify,
    knockoutMatchesWon: currentKnockoutMatchesWon,
    knockoutMatchesNeeded: 4,
    currentRun: worldCupRunCount,
    completedRuns: completedRuns.sort((a, b) => b.date.getTime() - a.date.getTime()) // Sort by date, newest first
  };
};

// Calculate the best win streak for a player
const calculateBestWinStreak = (matches: Match[], playerId: number): number => {
  // Get matches where this player participated, sorted by date (oldest first)
  const playerMatches = matches
    .filter(match => (match.teamA?.includes(playerId) || match.teamB?.includes(playerId)))
    .sort((a, b) => {
      const dateA = a.date instanceof Date ? a.date : new Date(a.date);
      const dateB = b.date instanceof Date ? b.date : new Date(b.date);
      return dateA.getTime() - dateB.getTime(); // Sort oldest first
    });

  if (playerMatches.length === 0) return 0;

  let bestStreak = 0;
  let currentStreak = 0;

  // Process all matches to find the best streak
  for (let i = 0; i < playerMatches.length; i++) {
    const match = playerMatches[i];
    const isInTeamA = match.teamA?.includes(playerId);
    const isWinner = (isInTeamA && match.winner === 'A') || (!isInTeamA && match.winner === 'B');

    if (isWinner) {
      // Increment current streak
      currentStreak++;
      // Update best streak if current is better
      bestStreak = Math.max(bestStreak, currentStreak);
    } else {
      // Reset streak on loss or draw
      currentStreak = 0;
    }
  }

  return bestStreak;
};

const renderScoreDisplay = (match: Match) => {
  if (match.scoreA !== null && match.scoreB !== null) {
    return <>{match.scoreA} - {match.scoreB}</>;
  } else {
    if (match.winner === 'A') return <span className="text-green-600">TEAM A WIN</span>;
    if (match.winner === 'B') return <span className="text-green-600">TEAM B WIN</span>;
    if (match.winner === 'Draw') return <span className="text-yellow-600">DRAW</span>;
    return "? - ?";
  }
};

// Add this after the other helper functions
function getCombinations<T>(array: T[], k: number): T[][] {
  const result: T[][] = [];
  function combine(start: number, currentCombination: T[]) {
    if (currentCombination.length === k) {
      result.push([...currentCombination]);
      return;
    }
    for (let i = start; i < array.length; i++) {
      currentCombination.push(array[i]);
      combine(i + 1, currentCombination);
      currentCombination.pop();
    }
  }
  combine(0, []);
  return result;
}

// --- Component ---

interface ChemistryStats {
  duos: { played: number; wins: number; winRate: number; };
  trios: { played: number; wins: number; winRate: number; };
  quads: { played: number; wins: number; winRate: number; };
}

const calculatePlayerChemistry = (matches: Match[], playerId: number): ChemistryStats => {
  const stats = {
    duos: { played: 0, wins: 0, winRate: 0 },
    trios: { played: 0, wins: 0, winRate: 0 },
    quads: { played: 0, wins: 0, winRate: 0 }
  };

  matches.forEach(match => {
    const teamA = match.teamA || [];
    const teamB = match.teamB || [];
    const isInTeamA = teamA.includes(playerId);
    const isInTeamB = teamB.includes(playerId);
    const team = isInTeamA ? teamA : isInTeamB ? teamB : null;
    const isWinner = (isInTeamA && match.winner === 'A') || (isInTeamB && match.winner === 'B');

    if (team) {
      const teamSize = team.length;
      if (teamSize >= 2) stats.duos.played++;
      if (teamSize >= 3) stats.trios.played++;
      if (teamSize >= 4) stats.quads.played++;

      if (isWinner) {
        if (teamSize >= 2) stats.duos.wins++;
        if (teamSize >= 3) stats.trios.wins++;
        if (teamSize >= 4) stats.quads.wins++;
      }
    }
  });

  // Calculate win rates
  stats.duos.winRate = stats.duos.played > 0 ? (stats.duos.wins / stats.duos.played) * 100 : 0;
  stats.trios.winRate = stats.trios.played > 0 ? (stats.trios.wins / stats.trios.played) * 100 : 0;
  stats.quads.winRate = stats.quads.played > 0 ? (stats.quads.wins / stats.quads.played) * 100 : 0;

  return stats;
};

const calculatePlayerBestChemistry = (matches: Match[], playerId: number, minGamesForChemistry: number): PlayerChemistryStats => {
  const stats = {
    duos: new Map<string, {players: number[], played: number, wins: number}>(),
    trios: new Map<string, {players: number[], played: number, wins: number}>(),
    quads: new Map<string, {players: number[], played: number, wins: number}>()
  };

  matches.forEach(match => {
    const teamA = match.teamA || [];
    const teamB = match.teamB || [];
    const isInTeamA = teamA.includes(playerId);
    const isInTeamB = teamB.includes(playerId);
    const team = isInTeamA ? teamA : isInTeamB ? teamB : null;
    const isWinner = (isInTeamA && match.winner === 'A') || (isInTeamB && match.winner === 'B');

    if (team) {
      // Process all combinations that include the selected player
      const processGroup = (size: 2 | 3 | 4, statsMap: Map<string, any>) => {
        if (team.length >= size) {
          const combinations = getCombinations(team.filter(id => id !== playerId), size - 1);
          combinations.forEach(combo => {
            const group = [...combo, playerId].sort((a, b) => a - b);
            const key = group.join('-');
            const existing = statsMap.get(key) || { players: group, played: 0, wins: 0 };
            existing.played++;
            if (isWinner) existing.wins++;
            statsMap.set(key, existing);
          });
        }
      };

      processGroup(2, stats.duos);
      processGroup(3, stats.trios);
      processGroup(4, stats.quads);
    }
  });

  // Convert to arrays and calculate win rates
  const processStats = (statsMap: Map<string, any>): BestChemistry[] => {
    const minGames = minGamesForChemistry;
    return Array.from(statsMap.values())
      .filter(stat => stat.played >= minGames)
      .map(stat => ({
        ...stat,
        winRate: (stat.played > 0 ? (stat.wins / stat.played) * 100 : 0)
      }))
      .sort((a, b) => b.winRate - a.winRate)
      .slice(0, 1); // Get only the top 1 best pair
  };

  return {
    duos: processStats(stats.duos),
    trios: processStats(stats.trios),
    quads: processStats(stats.quads)
  };
};

// We're now using the currentGroup from the GroupContext instead of localStorage

const DashboardPage = () => {
  const { t } = useTranslation();
  const { user, hasLoadedGroups } = useAuth();
  const { currentGroup, isLoading: isGroupLoading } = useGroup();
  const { toast } = useToast();
  const navigate = useNavigate();

  const [isLoading, setIsLoading] = useState(true);
  const [players, setPlayers] = useState<Player[]>([]);
  const [matches, setMatches] = useState<Match[]>([]);
  const [selectedPlayerId, setSelectedPlayerId] = useState<string>("");
  const [selectedPlayerData, setSelectedPlayerData] = useState<any | null>(null);
  const [isPlayerDataLoading, setIsPlayerDataLoading] = useState(false);
  const [minGamesForWinRate, setMinGamesForWinRate] = useState<number>(0);
  const [minGamesForLeaderboard, setMinGamesForLeaderboard] = useState<number>(0);
  const [activeTab, setActiveTab] = useState<string>("overview");
  const { preferences } = useUserPreferences();

  // Update playerStatsWithWinRate to use state
  const playerStatsWithWinRate = useMemo(() => {
    const statsMap: { [key: number]: { played: number; wins: number; draws: number; losses: number } } = {};

    players.forEach(p => {
      statsMap[p.id] = { played: 0, wins: 0, draws: 0, losses: 0 };
    });

    matches.forEach(m => {
      const involvedPlayers = [...(m.teamA || []), ...(m.teamB || [])];
      involvedPlayers.forEach(playerId => {
        if (statsMap[playerId]) {
          statsMap[playerId].played++;
          const isOnTeamA = m.teamA?.includes(playerId);
          if (m.winner === 'Draw') {
            statsMap[playerId].draws++;
          } else if ((isOnTeamA && m.winner === 'A') || (!isOnTeamA && m.winner === 'B')) {
            statsMap[playerId].wins++;
          } else if (m.winner) {
            statsMap[playerId].losses++;
          }
        }
      });
    });

    return players.map(player => {
      const stats = statsMap[player.id] ?? { played: 0, wins: 0, draws: 0, losses: 0 };
      const winRate = stats.played > 0 ? (stats.wins / stats.played) * 100 : 0;
      return { ...player, ...stats, winRate };
    });

  }, [players, matches]);

  // Calculate maximum games played by any player
  const maxGamesPlayed = useMemo(() => {
    if (playerStatsWithWinRate.length === 0) return 0;
    return Math.max(...playerStatsWithWinRate.map(p => p.played));
  }, [playerStatsWithWinRate]);

  // Calculate Top Win Rate player
  const topWinRatePlayer = useMemo(() => {
    const eligiblePlayers = playerStatsWithWinRate.filter(p => p.played >= minGamesForWinRate);
    if (eligiblePlayers.length === 0) return null;
    return [...eligiblePlayers].sort((a, b) => b.winRate - a.winRate)[0];
  }, [minGamesForWinRate, playerStatsWithWinRate]);

  // Calculate Top Leaderboard player (by win rate)
  const topLeaderboardPlayer = useMemo(() => {
    const minGames = parseInt(minGamesForLeaderboard, 10);
    const eligiblePlayers = playerStatsWithWinRate.filter(p => p.played >= minGames);
    if (eligiblePlayers.length === 0) return null;

    // Process players to include avgRating
    const playersWithRating = eligiblePlayers.map(player => ({
      ...player,
      avgRating: calculateAverageRating(player)
    }));

    // Sort by win rate
    return [...playersWithRating].sort((a, b) => b.winRate - a.winRate)[0];
  }, [minGamesForLeaderboard, playerStatsWithWinRate]);

  // Calculate best chemistry combinations (duos, trios, quads)
  const [minGamesForChemistry, setMinGamesForChemistry] = useState<number>(3);
  const bestChemistry = useMemo(() => {
    const minGames = minGamesForChemistry;

    // Helper function to get combinations
    const getCombinations = (arr: number[], size: number): number[][] => {
      const result: number[][] = [];

      function backtrack(start: number, current: number[]) {
        if (current.length === size) {
          result.push([...current]);
          return;
        }

        for (let i = start; i < arr.length; i++) {
          current.push(arr[i]);
          backtrack(i + 1, current);
          current.pop();
        }
      }

      backtrack(0, []);
      return result;
    };

    // Initialize maps for duos, trios, and quads
    const duosMap = new Map<string, { players: number[]; played: number; wins: number }>();
    const triosMap = new Map<string, { players: number[]; played: number; wins: number }>();
    const quadsMap = new Map<string, { players: number[]; played: number; wins: number }>();

    // Process each match
    matches.forEach(match => {
      const teamA = match.teamA || [];
      const teamB = match.teamB || [];

      // Process team A combinations
      if (teamA.length >= 2) {
        const duoCombos = getCombinations(teamA, 2);
        duoCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = duosMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'A') existing.wins++;
          duosMap.set(key, existing);
        });
      }

      if (teamA.length >= 3) {
        const trioCombos = getCombinations(teamA, 3);
        trioCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = triosMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'A') existing.wins++;
          triosMap.set(key, existing);
        });
      }

      if (teamA.length >= 4) {
        const quadCombos = getCombinations(teamA, 4);
        quadCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = quadsMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'A') existing.wins++;
          quadsMap.set(key, existing);
        });
      }

      // Process team B combinations
      if (teamB.length >= 2) {
        const duoCombos = getCombinations(teamB, 2);
        duoCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = duosMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'B') existing.wins++;
          duosMap.set(key, existing);
        });
      }

      if (teamB.length >= 3) {
        const trioCombos = getCombinations(teamB, 3);
        trioCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = triosMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'B') existing.wins++;
          triosMap.set(key, existing);
        });
      }

      if (teamB.length >= 4) {
        const quadCombos = getCombinations(teamB, 4);
        quadCombos.forEach(combo => {
          const key = combo.sort((a, b) => a - b).join('-');
          const existing = quadsMap.get(key) || { players: combo, played: 0, wins: 0 };
          existing.played++;
          if (match.winner === 'B') existing.wins++;
          quadsMap.set(key, existing);
        });
      }
    });

    // Convert to arrays and calculate win rates
    const processChemistryData = (statsMap: Map<string, any>) => {
      return Array.from(statsMap.values())
        .filter(stat => stat.played >= minGames)
        .map(stat => ({
          ...stat,
          winRate: (stat.played > 0 ? (stat.wins / stat.played) * 100 : 0)
        }))
        .sort((a, b) => b.winRate - a.winRate)
        .slice(0, 1); // Get only the top 1
    };

    return {
      duos: processChemistryData(duosMap),
      trios: processChemistryData(triosMap),
      quads: processChemistryData(quadsMap)
    };
  }, [matches, minGamesForChemistry]);

  // Function to calculate player data
  const getPlayerData = (playerId: number) => {
    const player = playerStatsWithWinRate.find(p => p.id === playerId);
    if (!player) return null;

    // Calculate rank based on win rate and minimum games filter
    const minGames = parseInt(minGamesForLeaderboard, 10);
    const eligiblePlayers = playerStatsWithWinRate
      .filter(p => p.played >= minGames)
      .sort((a, b) => b.winRate - a.winRate);

    const rank = eligiblePlayers.findIndex(p => p.id === playerId) + 1;

    // Find last match played by this player from matches state
    const playerMatches = matches.filter(m => m.teamA?.includes(playerId) || m.teamB?.includes(playerId));
    const lastMatchPlayed = [...playerMatches]
      .filter(m => m.date instanceof Date && !isNaN(m.date.getTime()))
      .sort((a, b) => b.date.getTime() - a.date.getTime())[0];

    // Calculate chemistry stats
    const chemistryStats = calculatePlayerChemistry(matches, playerId);

    // Calculate best chemistry combinations
    const bestChemistry = calculatePlayerBestChemistry(matches, playerId, minGamesForChemistry);

    // Calculate World Cup Run stat
    const worldCupRunData = calculateWorldCupRun(matches, playerId);

    // Calculate best win streak
    const bestWinStreak = calculateBestWinStreak(matches, playerId);

    // Calculate recent matches for recent form display
    const recentMatches = playerMatches
      .slice(0, 7) // Get last 7 matches
      .map(match => {
        const isInTeamA = match.teamA?.includes(playerId);
        let result = 'L'; // Default to loss
        if (match.winner === 'Draw') {
          result = 'D';
        } else if ((isInTeamA && match.winner === 'A') || (!isInTeamA && match.winner === 'B')) {
          result = 'W';
        }
        return {
          date: match.date,
          result
        };
      });

    return {
      ...player,
      rank,
      lastMatch: lastMatchPlayed,
      avgRating: calculateAverageRating(player),
      chemistry: chemistryStats,
      bestChemistry,
      worldCupRun: worldCupRunData, // Fix the property name
      bestWinStreak,
      recentMatches
    };
  };

  // Memoized selected player data
  const selectedPlayerDataMemo = useMemo(() => {
    if (!selectedPlayerId) return null;
    const playerId = parseInt(selectedPlayerId, 10);
    return getPlayerData(playerId);
  }, [selectedPlayerId, playerStatsWithWinRate, players, matches, minGamesForChemistry]);

  // Find the last overall match
  const lastOverallMatch = useMemo(() => {
    if (matches.length === 0) return null;
    return [...matches]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0];
  }, [matches]);

  // Find the oldest match (for "Playing Since" info)
  const oldestMatch = useMemo(() => {
    if (matches.length === 0) return null;
    return [...matches]
      .sort((a, b) => {
        const dateA = a.date instanceof Date ? a.date :
                   a.match_date instanceof Date ? a.match_date :
                   new Date(a.match_date || a.date || Date.now());
        const dateB = b.date instanceof Date ? b.date :
                   b.match_date instanceof Date ? b.match_date :
                   new Date(b.match_date || b.date || Date.now());
        return dateA.getTime() - dateB.getTime();
      })[0];
  }, [matches]);

  // Update getPlayerName to use players state instead of playersData
  const getPlayerName = (id: number): string => {
    const player = players.find((p) => p.id === id);
    return player ? player.name : `Player ${id}`;
  };

  // Effect to update selectedPlayerData when a player is selected
  useEffect(() => {
    if (!selectedPlayerId) {
      setSelectedPlayerData(null);
      return;
    }

    setIsPlayerDataLoading(true);

    // Simulate a delay to show the skeleton loader
    setTimeout(() => {
      const playerId = parseInt(selectedPlayerId);
      const playerData = getPlayerData(playerId);
      setSelectedPlayerData(playerData);
      setIsPlayerDataLoading(false);
    }, 500);
  }, [selectedPlayerId, playerStatsWithWinRate, players, matches, minGamesForChemistry]);

  // Fetch players and matches when currentGroup changes
  // Using currentGroup.id as dependency instead of the entire object
  useEffect(() => {
    const fetchData = async () => {
      if (isGroupLoading) {
        console.log('Group context is still loading...');
        return;
      }

      if (!currentGroup) {
        console.log('No current group selected');
        setPlayers([]);
        setMatches([]);
        setIsLoading(false);
        return;
      }

      if (!hasLoadedGroups) {
        console.log('Groups have not been loaded yet');
        return;
      }

      console.log('Fetching data for group:', currentGroup.name, currentGroup.id);
      setIsLoading(true);
      try {
        // Always fetch fresh data from Supabase based on the current group
        const { data: playersResult, error: playersError } = await supabase
          .from('players')
          .select('*')
          .or(`group_id.eq.${currentGroup.id},group_id.is.null`);

        if (playersError) throw playersError;
        const playersData = playersResult as Player[];
        console.log('Fetched players:', playersData.length);

        const { data: matchesResult, error: matchesError } = await supabase
          .from('matches')
          .select('*')
          .or(`group_id.eq.${currentGroup.id},group_id.is.null`);

        if (matchesError) throw matchesError;
        let matchesData = matchesResult as Match[];
        console.log('Fetched matches:', matchesData.length);

        // Convert date strings to Date objects and normalize property names
        matchesData = matchesData.map(match => ({
          ...match,
          date: match.match_date ? new Date(match.match_date) : new Date(),
          teamA: match.teama || match.teamA || [],
          teamB: match.teamb || match.teamB || [],
          scoreA: match.scorea || match.scoreA,
          scoreB: match.scoreb || match.scoreB
        }));

        setPlayers(playersData);
        setMatches(matchesData);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: t('errors.databaseError'),
          description: t('errors.tryAgain'),
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [currentGroup?.id, toast, isGroupLoading, hasLoadedGroups]);

  // Log when currentGroup changes - no dependencies to avoid infinite loops
  useEffect(() => {
    if (currentGroup) {
      console.log('Current group loaded:', currentGroup.name, currentGroup.id);
    }
  }, []);

  // Group management has been moved to the Group Selection page

  if (isLoading || isGroupLoading || !hasLoadedGroups) {
    return (
      <DashboardSkeleton />
    );
  }

  // Empty states for no players or matches
  const hasNoPlayers = players.length === 0;
  const hasNoMatches = matches.length === 0;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold gradient-text-primary">{t('nav.dashboard')}</h1>
      </div>

      {/* Show empty state if no data at all */}
      {hasNoPlayers && hasNoMatches ? (
        <EmptyState
          icon={Users}
          title={t('dashboard.welcome')}
          description={t('dashboard.getStarted')}
          actionLabel={t('dashboard.addFirstPlayer')}
          actionIcon={UserPlus}
          onAction={() => navigate('/players')}
          className="bg-card border-border py-12"
        />
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 mb-6">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="overview" className="dashboard-tab">
                    <LayoutDashboard className="h-4 w-4 md:mr-2 tab-icon" />
                    <span className="hidden md:inline tab-label">{t('dashboard.overview')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  <p>{t('dashboard.overview')}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="leaderboard" className="dashboard-tab">
                    <Medal className="h-4 w-4 md:mr-2 tab-icon" />
                    <span className="hidden md:inline tab-label">{t('nav.leaderboard')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  <p>{t('nav.leaderboard')}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="chemistry" className="dashboard-tab">
                    <Handshake className="h-4 w-4 md:mr-2 tab-icon" />
                    <span className="hidden md:inline tab-label">{t('nav.chemistry')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  <p>{t('nav.chemistry')}</p>
                </TooltipContent>
              </Tooltip>

              <Tooltip>
                <TooltipTrigger asChild>
                  <TabsTrigger value="team-generator" className="dashboard-tab">
                    <Shuffle className="h-4 w-4 md:mr-2 tab-icon" />
                    <span className="hidden md:inline tab-label">{t('nav.teamGenerator')}</span>
                  </TabsTrigger>
                </TooltipTrigger>
                <TooltipContent className="md:hidden">
                  <p>{t('nav.teamGenerator')}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </TabsList>

          <TabsContent value="overview">
            <div className="flex justify-start mb-4">
              <div className="flex items-center gap-4 w-full max-w-md">
                <div className="text-sm text-muted-foreground whitespace-nowrap">
                  Min. Games: {minGamesForLeaderboard}
                </div>
                <div className="flex-1">
                  <Slider
                    value={[minGamesForLeaderboard]}
                    onValueChange={(value) => {
                      const newValue = value[0];
                      setMinGamesForLeaderboard(newValue);
                      setMinGamesForChemistry(newValue);
                      setMinGamesForWinRate(newValue);
                    }}
                    max={maxGamesPlayed}
                    min={0}
                    step={1}
                    className="w-full"
                    aria-label={`Minimum games filter, current value: ${minGamesForLeaderboard}, maximum: ${maxGamesPlayed}`}
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Group Stats Card - Enhanced */}
              <EnhancedCard hoverable className="lg:col-span-1 group">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-gradient-to-br from-soccer-primary to-soccer-primary-light p-2 rounded-xl shadow-sm">
                        <Users className="h-5 w-5 text-white" />
                      </div>
                      <div className="font-semibold text-lg gradient-text-primary">{t('dashboard.groupStats')}</div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8 rounded-full hover:bg-soccer-primary/10 hover:text-soccer-primary hover:scale-105 transition-all duration-200"
                        onClick={() => navigate('/players')}
                        title={t('players.addPlayer')}
                      >
                        <UserPlus className="h-4 w-4" />
                      </Button>
                      <Button
                        size="icon"
                        variant="ghost"
                        className="h-8 w-8 rounded-full hover:bg-soccer-primary/10 hover:text-soccer-primary hover:scale-105 transition-all duration-200"
                        onClick={() => navigate('/matches')}
                        title={t('matches.addMatch')}
                      >
                        <CalendarPlus className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-6">
                    <div className="flex items-center gap-4 p-3 rounded-xl bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20 hover:border-soccer-primary/30 transition-all duration-300">
                      <div className="bg-gradient-to-br from-soccer-primary to-soccer-primary-light p-3 rounded-xl shadow-sm">
                        <Users className="h-5 w-5 text-white" aria-hidden="true" />
                      </div>
                      <div className="flex-1">
                        <div className="text-2xl font-bold text-soccer-primary">{players.length}</div>
                        <div className="text-sm font-medium text-muted-foreground">{t('dashboard.totalPlayers')}</div>
                      </div>
                      {hasNoPlayers && (
                        <AnimatedButton
                          size="sm"
                          onClick={() => navigate('/players')}
                          animation="scale"
                          icon={<UserPlus className="h-3 w-3" />}
                          variant="outline"
                          className="border-soccer-primary text-soccer-primary hover:bg-soccer-primary hover:text-white"
                        >
                          {t('players.addPlayer')}
                        </AnimatedButton>
                      )}
                    </div>

                    <div className="flex items-center gap-4 p-3 rounded-xl bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20 hover:border-soccer-primary/30 transition-all duration-300">
                      <div className="bg-gradient-to-br from-soccer-primary to-soccer-primary-light p-3 rounded-xl shadow-sm">
                        <Trophy className="h-5 w-5 text-white" aria-hidden="true" />
                      </div>
                      <div className="flex-1">
                        <div className="text-2xl font-bold text-soccer-primary">{matches.length}</div>
                        <div className="text-sm font-medium text-muted-foreground">{t('dashboard.totalMatches')}</div>
                      </div>
                      {hasNoMatches && (
                        <AnimatedButton
                          size="sm"
                          onClick={() => navigate('/matches')}
                          animation="scale"
                          icon={<CalendarPlus className="h-3 w-3" />}
                          variant="outline"
                          className="border-soccer-primary text-soccer-primary hover:bg-soccer-primary hover:text-white"
                        >
                          {t('matches.addMatch')}
                        </AnimatedButton>
                      )}
                    </div>

                    {oldestMatch && (
                      <div className="text-xs text-muted-foreground p-3 rounded-lg bg-gradient-to-r from-muted/30 to-muted/50 border border-muted">
                        <div className="flex flex-col items-center text-center">
                          <span className="font-medium text-sm">Playing Since</span>
                          <span className="font-bold text-lg text-soccer-primary">
                            {format(
                              oldestMatch.date instanceof Date ? oldestMatch.date :
                              oldestMatch.match_date instanceof Date ? oldestMatch.match_date :
                              new Date(oldestMatch.match_date || oldestMatch.date || Date.now()),
                              "MMM d, yyyy"
                            )}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </EnhancedCard>

              {/* Leaderboard Leader Card - Enhanced */}
              <EnhancedCard hoverable className="lg:col-span-1 group">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-gradient-to-br from-soccer-primary to-soccer-primary-light p-2 rounded-xl shadow-sm">
                        <Crown className="h-5 w-5 text-white" />
                      </div>
                      <div className="font-semibold text-lg gradient-text-primary">{t('dashboard.leader', 'Leader')}</div>
                    </div>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-8 w-8 rounded-full hover:bg-soccer-primary/10 hover:text-soccer-primary hover:scale-105 transition-all duration-200"
                      onClick={() => setActiveTab("leaderboard")}
                      title={t('nav.leaderboard')}
                    >
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>

                  {topLeaderboardPlayer ? (
                    <div className="space-y-6">
                      {/* Player Name and Avatar */}
                      <div className="text-center p-4 rounded-xl bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20">
                        <div className="flex items-center justify-center mb-3">
                          <div className="bg-gradient-to-br from-soccer-primary to-soccer-primary-light p-4 rounded-full shadow-lg">
                            <Crown className="h-6 w-6 text-white" aria-hidden="true" />
                          </div>
                        </div>
                        <div className="text-xl font-bold text-soccer-primary">{topLeaderboardPlayer.name}</div>
                        <div className="text-sm font-medium text-muted-foreground">{t('dashboard.currentLeader', 'Current Leader')}</div>
                      </div>

                      {/* Stats Grid */}
                      <div className="grid grid-cols-3 gap-3">
                        <div className="text-center p-3 rounded-lg bg-gradient-to-br from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20">
                          <Medal className="h-5 w-5 text-soccer-primary mx-auto mb-1" />
                          <div className="text-lg font-bold text-soccer-primary">{topLeaderboardPlayer.avgRating}</div>
                          <div className="text-xs text-muted-foreground">{t('players.rating')}</div>
                        </div>
                        <div className="text-center p-3 rounded-lg bg-gradient-to-br from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20">
                          <Percent className="h-5 w-5 text-soccer-primary mx-auto mb-1" />
                          <div className="text-lg font-bold text-soccer-primary">{topLeaderboardPlayer.winRate.toFixed(1)}%</div>
                          <div className="text-xs text-muted-foreground">{t('players.winRate')}</div>
                        </div>
                        <div className="text-center p-3 rounded-lg bg-gradient-to-br from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20">
                          <Calendar className="h-5 w-5 text-soccer-primary mx-auto mb-1" />
                          <div className="text-lg font-bold text-soccer-primary">{topLeaderboardPlayer.played}</div>
                          <div className="text-xs text-muted-foreground">{t('players.played')}</div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 space-y-4">
                      <div className="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 p-6 rounded-full">
                        <Crown className="h-8 w-8 text-muted-foreground opacity-50" />
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium text-muted-foreground">{t('leaderboard.noPlayersFound')}</p>
                        <p className="text-xs text-muted-foreground mt-1">{t('dashboard.addPlayersToSeeLeader', 'Add players to see the leader')}</p>
                      </div>
                    </div>
                  )}
                </div>
              </EnhancedCard>

              {/* Chemistry Card - Enhanced */}
              <EnhancedCard hoverable className="lg:col-span-1 group">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-gradient-to-br from-soccer-primary to-soccer-primary-light p-2 rounded-xl shadow-sm">
                        <Handshake className="h-5 w-5 text-white" />
                      </div>
                      <div className="font-semibold text-lg gradient-text-primary">{t('chemistry.title', 'Chemistry')}</div>
                    </div>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="h-8 w-8 rounded-full hover:bg-soccer-primary/10 hover:text-soccer-primary hover:scale-105 transition-all duration-200"
                      onClick={() => setActiveTab("chemistry")}
                      title={t('nav.chemistry')}
                    >
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </div>

                  {bestChemistry.duos.length > 0 || bestChemistry.trios.length > 0 || bestChemistry.quads.length > 0 ? (
                    <div className="space-y-4">
                      {bestChemistry.duos.length > 0 && (
                        <div className="p-3 rounded-lg bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20">
                          <div className="flex items-center gap-2 mb-2">
                            <UsersRound className="h-4 w-4 text-soccer-primary" />
                            <span className="text-sm font-medium text-soccer-primary">{t('chemistry.bestDuo', 'Best Duo')}</span>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {bestChemistry.duos[0].players.map(id => getPlayerName(id)).join(' + ')}
                          </div>
                          <div className="text-xs text-soccer-primary font-medium">
                            {bestChemistry.duos[0].winRate.toFixed(1)}% ({bestChemistry.duos[0].played} games)
                          </div>
                        </div>
                      )}

                      {bestChemistry.trios.length > 0 && (
                        <div className="p-3 rounded-lg bg-gradient-to-r from-muted/30 to-muted/50 border border-muted">
                          <div className="flex items-center gap-2 mb-2">
                            <Users className="h-4 w-4 text-soccer-primary" />
                            <span className="text-sm font-medium text-soccer-primary">{t('chemistry.bestTrio', 'Best Trio')}</span>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {bestChemistry.trios[0].players.map(id => getPlayerName(id)).join(' + ')}
                          </div>
                          <div className="text-xs text-soccer-primary font-medium">
                            {bestChemistry.trios[0].winRate.toFixed(1)}% ({bestChemistry.trios[0].played} games)
                          </div>
                        </div>
                      )}

                      {bestChemistry.quads.length > 0 && (
                        <div className="p-3 rounded-lg bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20">
                          <div className="flex items-center gap-2 mb-2">
                            <Users className="h-4 w-4 text-soccer-primary" />
                            <span className="text-sm font-medium text-soccer-primary">{t('chemistry.bestQuad', 'Best Quad')}</span>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {bestChemistry.quads[0].players.map(id => getPlayerName(id)).join(' + ')}
                          </div>
                          <div className="text-xs text-soccer-primary font-medium">
                            {bestChemistry.quads[0].winRate.toFixed(1)}% ({bestChemistry.quads[0].played} games)
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 space-y-4">
                      <div className="bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900 p-6 rounded-full">
                        <Handshake className="h-8 w-8 text-muted-foreground opacity-50" />
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium text-muted-foreground">{t('chemistry.noData', 'No chemistry data available')}</p>
                        <p className="text-xs text-muted-foreground mt-1">{t('chemistry.playMoreMatches', 'Play more matches to see chemistry')}</p>
                      </div>
                    </div>
                  )}
                </div>
              </EnhancedCard>
            </div>

            {/* Player Snapshot Section */}
            <EnhancedCard className="mt-8">
              <EnhancedCard.Header>
                <EnhancedCard.Title className="flex items-center gap-2">
                  <User className="h-5 w-5 text-soccer-primary" />
                  {t('dashboard.playerSnapshot', 'Player Snapshot')}
                </EnhancedCard.Title>
              </EnhancedCard.Header>
              <EnhancedCard.Content>
                <div className="flex items-center gap-4 mb-6">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="player-select" className="text-sm font-medium">
                      {t('dashboard.selectPlayer', 'Select Player')}:
                    </Label>
                    <Select value={selectedPlayerId} onValueChange={setSelectedPlayerId}>
                      <SelectTrigger className="w-[200px]" id="player-select">
                        <SelectValue placeholder={t('dashboard.choosePlayer', 'Choose a player')} />
                      </SelectTrigger>
                      <SelectContent>
                        {playerStatsWithWinRate
                          .sort((a, b) => b.played - a.played)
                          .map((player) => (
                            <SelectItem key={player.id.toString()} value={player.id.toString()}>
                              {player.name} ({player.played} {t('players.matches', 'matches')})
                            </SelectItem>
                          ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {selectedPlayerData && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    {/* Player Stats */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-lg flex items-center gap-2">
                        <TrendingUp className="h-4 w-4 text-soccer-primary" />
                        {t('dashboard.playerStats', 'Player Stats')}
                      </h4>
                      <div className="space-y-3">
                        <div className="flex justify-between items-center p-2 rounded bg-muted/50">
                          <span className="text-sm">{t('players.played')}</span>
                          <span className="font-medium">{selectedPlayerData.played}</span>
                        </div>
                        <div className="flex justify-between items-center p-2 rounded bg-muted/50">
                          <span className="text-sm">{t('players.winRate')}</span>
                          <span className="font-medium text-green-600">{selectedPlayerData.winRate.toFixed(1)}%</span>
                        </div>
                        <div className="flex justify-between items-center p-2 rounded bg-muted/50">
                          <span className="text-sm">{t('players.rating')}</span>
                          <span className="font-medium text-blue-600">{selectedPlayerData.avgRating}</span>
                        </div>
                        <div className="flex justify-between items-center p-2 rounded bg-muted/50">
                          <span className="text-sm">{t('dashboard.bestWinStreak', 'Best Win Streak')}</span>
                          <span className="font-medium text-purple-600">{selectedPlayerData.bestWinStreak || 0}</span>
                        </div>
                      </div>
                    </div>

                    {/* World Cup Run */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-lg flex items-center gap-2">
                        <Award className="h-4 w-4 text-amber-500" />
                        {t('dashboard.worldCupRun', 'Mundialito')}
                      </h4>
                      <div className="space-y-3">
                        {selectedPlayerData.worldCupRun && selectedPlayerData.worldCupRun.status !== 'not-started' ? (
                          <div className="space-y-3">
                            {/* Current Status */}
                            <div className="p-3 rounded-lg bg-gradient-to-r from-soccer-primary/5 to-soccer-primary/10 border border-soccer-primary/20">
                              <div className="flex items-center gap-2 mb-2">
                                <Trophy className={`h-4 w-4 ${
                                  selectedPlayerData.worldCupRun.status === 'qualified' ? 'text-green-500' :
                                  selectedPlayerData.worldCupRun.status === 'eliminated' ? 'text-red-500' :
                                  'text-soccer-primary'
                                }`} />
                                <span className="text-sm font-medium text-soccer-primary">
                                  {selectedPlayerData.worldCupRun.status === 'qualified' ?
                                    `Knockouts (${selectedPlayerData.worldCupRun.knockoutMatchesWon}/4 wins)` :
                                    selectedPlayerData.worldCupRun.status === 'in-progress' ?
                                    `Qualifying (${selectedPlayerData.worldCupRun.groupStagePoints}/9 pts)` :
                                    selectedPlayerData.worldCupRun.status === 'eliminated' ? 'Eliminated' :
                                    selectedPlayerData.worldCupRun.status
                                  }
                                </span>
                              </div>
                              {selectedPlayerData.worldCupRun.status === 'in-progress' && (
                                <div className="text-xs text-muted-foreground">
                                  {selectedPlayerData.worldCupRun.groupStageMatchesPlayed}/3 matches played
                                </div>
                              )}
                            </div>

                            {/* Completed World Cup Runs */}
                            {selectedPlayerData.worldCupRun.completedRuns && selectedPlayerData.worldCupRun.completedRuns.length > 0 && (
                              <div className="space-y-2">
                                <div className="text-xs font-medium text-muted-foreground">World Cup Runs Won:</div>
                                <div className="flex flex-wrap gap-2">
                                  {selectedPlayerData.worldCupRun.completedRuns.map((run, index) => (
                                    <div
                                      key={index}
                                      className="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-300 border border-amber-200 dark:border-amber-800"
                                    >
                                      <Trophy className="h-3 w-3" />
                                      {format(run.date, "MMM d, yyyy")}
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="p-3 rounded-lg bg-muted/50 text-center">
                            <Trophy className="h-6 w-6 mx-auto mb-2 text-muted-foreground opacity-50" />
                            <p className="text-xs text-muted-foreground">{t('dashboard.noWorldCupRun', 'No active run')}</p>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Chemistry Summary */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-lg flex items-center gap-2">
                        <Handshake className="h-4 w-4 text-purple-500" />
                        {t('dashboard.chemistryHighlights', 'Chemistry')}
                      </h4>
                      <div className="space-y-3">
                        {selectedPlayerData.bestChemistry.duos.length > 0 && (
                          <div className="p-2 rounded bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
                            <div className="text-xs font-medium text-blue-700 dark:text-blue-300 mb-1">{t('chemistry.bestDuo', 'Best Duo')}</div>
                            <div className="text-xs text-blue-600 dark:text-blue-400">
                              {selectedPlayerData.bestChemistry.duos[0].players
                                .filter(id => id !== parseInt(selectedPlayerId))
                                .map(id => getPlayerName(id))
                                .join(', ')}
                            </div>
                            <div className="text-xs text-green-600 dark:text-green-400 font-medium">
                              {selectedPlayerData.bestChemistry.duos[0].winRate.toFixed(1)}%
                            </div>
                          </div>
                        )}
                        {selectedPlayerData.bestChemistry.trios.length > 0 && (
                          <div className="p-2 rounded bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800">
                            <div className="text-xs font-medium text-green-700 dark:text-green-300 mb-1">{t('chemistry.bestTrio', 'Best Trio')}</div>
                            <div className="text-xs text-green-600 dark:text-green-400">
                              {selectedPlayerData.bestChemistry.trios[0].players
                                .filter(id => id !== parseInt(selectedPlayerId))
                                .map(id => getPlayerName(id))
                                .join(', ')}
                            </div>
                            <div className="text-xs text-green-600 dark:text-green-400 font-medium">
                              {selectedPlayerData.bestChemistry.trios[0].winRate.toFixed(1)}%
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Recent Form */}
                    <div className="space-y-4">
                      <h4 className="font-semibold text-lg flex items-center gap-2">
                        <Flame className="h-4 w-4 text-orange-500" />
                        {t('dashboard.recentForm', 'Recent Form')}
                      </h4>
                      <div className="space-y-3">
                        {selectedPlayerData.recentMatches && selectedPlayerData.recentMatches.length > 0 ? (
                          <div className="space-y-2">
                            {selectedPlayerData.recentMatches.slice(0, 5).map((match, index) => (
                              <div key={index} className="flex items-center justify-between p-2 rounded bg-muted/50">
                                <span className="text-xs text-muted-foreground">
                                  {format(new Date(match.date), "MMM d")}
                                </span>
                                <span className={`text-xs font-medium px-2 py-1 rounded ${
                                  match.result === 'W' ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400' :
                                  match.result === 'D' ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400' :
                                  'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400'
                                }`}>
                                  {match.result}
                                </span>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="p-3 rounded-lg bg-muted/50 text-center">
                            <History className="h-6 w-6 mx-auto mb-2 text-muted-foreground opacity-50" />
                            <p className="text-xs text-muted-foreground">{t('dashboard.noRecentMatches', 'No recent matches')}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {!selectedPlayerId && (
                  <div className="text-center py-8">
                    <User className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                    <p className="text-muted-foreground">{t('dashboard.selectPlayerToView', 'Select a player to view their snapshot')}</p>
                  </div>
                )}
              </EnhancedCard.Content>
            </EnhancedCard>
          </TabsContent>

          <TabsContent value="leaderboard">
            <DashboardLeaderboard players={players} matches={matches} />
          </TabsContent>

          <TabsContent value="chemistry">
            <DashboardChemistry players={players} matches={matches} />
          </TabsContent>

          <TabsContent value="team-generator">
            <DashboardTeamGenerator
              players={playerStatsWithWinRate}
              matches={matches}
              onSaveMatch={async (newMatch) => {
                try {
                  // Add group_id to the match
                  const matchWithGroup = {
                    ...newMatch,
                    group_id: currentGroup?.id,
                    match_date: new Date().toISOString()
                  };

                  const { data, error } = await supabase
                    .from('matches')
                    .insert([matchWithGroup])
                    .select();

                  if (error) throw error;

                  // Update local state
                  setMatches(prev => [...prev, {
                    ...matchWithGroup,
                    id: data[0].id,
                    date: new Date()
                  } as Match]);

                } catch (error) {
                  console.error("Error saving match:", error);
                  toast({
                    title: t('matches.errorSaving'),
                    description: error.message,
                    variant: "destructive"
                  });
                }
              }}
            />
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
};

export default DashboardPage;
